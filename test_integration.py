"""
RLGymPPO DirectML Integration Test

This script tests the full RLGymPPO pipeline with DirectML to ensure
end-to-end compatibility and functionality.
"""

import sys
import time
import traceback
from typing import Optional

import numpy as np
import torch


def create_minimal_env():
    """Create a minimal test environment for testing."""
    
    class MinimalTestEnv:
        def __init__(self):
            self.observation_space = type('obj', (object,), {'shape': (107,)})()
            self.action_space = type('obj', (object,), {'n': 8})()
            self._step_count = 0
            self._max_steps = 100
            
        def reset(self):
            self._step_count = 0
            return np.random.randn(107).astype(np.float32)
            
        def step(self, action):
            self._step_count += 1
            obs = np.random.randn(107).astype(np.float32)
            reward = np.random.randn()
            done = self._step_count >= self._max_steps
            truncated = False
            info = {}
            return obs, reward, done, truncated, info
            
        def close(self):
            pass
    
    return MinimalTestEnv


def test_ppo_components():
    """Test PPO components with DirectML."""
    print("Testing PPO components...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        from rlgym_ppo.ppo import PPOLearner, ExperienceBuffer
        
        # Get device
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        print(f"Using device: {device} (type: {device_type})")
        
        # Create PPO learner
        obs_space_size = 107
        act_space_size = 8
        policy_type = 0  # Discrete
        policy_layer_sizes = [128, 128]
        critic_layer_sizes = [128, 128]
        continuous_var_range = [0.1, 1.0]
        batch_size = 1000
        n_epochs = 1
        policy_lr = 3e-4
        critic_lr = 3e-4
        clip_range = 0.2
        ent_coef = 0.01
        mini_batch_size = 500
        
        ppo_learner = PPOLearner(
            obs_space_size=obs_space_size,
            act_space_size=act_space_size,
            policy_type=policy_type,
            policy_layer_sizes=policy_layer_sizes,
            critic_layer_sizes=critic_layer_sizes,
            continuous_var_range=continuous_var_range,
            batch_size=batch_size,
            n_epochs=n_epochs,
            policy_lr=policy_lr,
            critic_lr=critic_lr,
            clip_range=clip_range,
            ent_coef=ent_coef,
            mini_batch_size=mini_batch_size,
            device=device
        )
        print("✓ PPO learner created successfully")
        
        # Create experience buffer
        exp_buffer = ExperienceBuffer(max_size=batch_size, seed=42, device="cpu")
        print("✓ Experience buffer created successfully")
        
        # Test policy inference
        test_obs = torch.randn(32, obs_space_size).to(device)
        with torch.no_grad():
            actions, log_probs = ppo_learner.policy.get_action(test_obs)
            values = ppo_learner.value_net(test_obs)
        
        print(f"✓ Policy inference: actions shape {actions.shape}, log_probs shape {log_probs.shape}")
        print(f"✓ Value inference: values shape {values.shape}")
        
        return True
        
    except Exception as e:
        print(f"✗ PPO components test failed: {e}")
        traceback.print_exc()
        return False


def test_experience_buffer_operations():
    """Test experience buffer operations."""
    print("\nTesting experience buffer operations...")
    
    try:
        from rlgym_ppo.ppo import ExperienceBuffer
        from rlgym_ppo.util import torch_functions
        
        # Create buffer
        buffer_size = 1000
        exp_buffer = ExperienceBuffer(max_size=buffer_size, seed=42, device="cpu")
        
        # Generate fake experience
        batch_size = 100
        obs_size = 107
        
        states = np.random.randn(batch_size, obs_size).astype(np.float32)
        actions = np.random.randint(0, 8, size=(batch_size,))
        log_probs = np.random.randn(batch_size).astype(np.float32)
        rewards = np.random.randn(batch_size).astype(np.float32)
        next_states = np.random.randn(batch_size, obs_size).astype(np.float32)
        dones = np.random.choice([True, False], size=(batch_size,))
        truncated = np.random.choice([True, False], size=(batch_size,))
        values = np.random.randn(batch_size + 1).astype(np.float32)  # +1 for bootstrap
        
        # Compute advantages using GAE
        value_targets, advantages, returns = torch_functions.compute_gae(
            rewards, dones, truncated, values.tolist(), gamma=0.99, lmbda=0.95
        )
        
        # Submit experience
        exp_buffer.submit_experience(
            states, actions, log_probs, rewards, next_states, 
            dones, truncated, values[:-1], advantages
        )
        
        print("✓ Experience submitted to buffer")
        
        # Test batch generation
        mini_batch_size = 50
        batches = list(exp_buffer.get_all_batches_shuffled(mini_batch_size))
        print(f"✓ Generated {len(batches)} mini-batches of size {mini_batch_size}")
        
        return True
        
    except Exception as e:
        print(f"✗ Experience buffer test failed: {e}")
        traceback.print_exc()
        return False


def test_training_step():
    """Test a single training step with DirectML."""
    print("\nTesting training step...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        from rlgym_ppo.ppo import PPOLearner, ExperienceBuffer
        from rlgym_ppo.util import torch_functions
        
        # Get device
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        
        # Create PPO learner (smaller for testing)
        ppo_learner = PPOLearner(
            obs_space_size=107,
            act_space_size=8,
            policy_type=0,
            policy_layer_sizes=[64, 64],
            critic_layer_sizes=[64, 64],
            continuous_var_range=[0.1, 1.0],
            batch_size=200,
            n_epochs=1,
            policy_lr=3e-4,
            critic_lr=3e-4,
            clip_range=0.2,
            ent_coef=0.01,
            mini_batch_size=100,
            device=device
        )
        
        # Create experience buffer
        exp_buffer = ExperienceBuffer(max_size=200, seed=42, device="cpu")
        
        # Generate training data
        batch_size = 200
        obs_size = 107
        
        states = np.random.randn(batch_size, obs_size).astype(np.float32)
        actions = np.random.randint(0, 8, size=(batch_size,))
        log_probs = np.random.randn(batch_size).astype(np.float32)
        rewards = np.random.randn(batch_size).astype(np.float32)
        next_states = np.random.randn(batch_size, obs_size).astype(np.float32)
        dones = np.random.choice([True, False], size=(batch_size,))
        truncated = np.random.choice([True, False], size=(batch_size,))
        values = np.random.randn(batch_size + 1).astype(np.float32)
        
        # Compute advantages
        value_targets, advantages, returns = torch_functions.compute_gae(
            rewards, dones, truncated, values.tolist(), gamma=0.99, lmbda=0.95
        )
        
        # Submit experience
        exp_buffer.submit_experience(
            states, actions, log_probs, rewards, next_states,
            dones, truncated, values[:-1], advantages
        )
        
        print("✓ Training data prepared")
        
        # Perform training step
        start_time = time.time()
        training_report = ppo_learner.learn(exp_buffer)
        end_time = time.time()
        
        print(f"✓ Training step completed in {end_time - start_time:.3f}s")
        print(f"✓ Training report keys: {list(training_report.keys())}")
        
        # Verify training metrics
        expected_keys = ['Policy Loss', 'Value Function Loss', 'Entropy', 'KL Divergence']
        for key in expected_keys:
            if key in training_report:
                print(f"  {key}: {training_report[key]:.6f}")
        
        return True
        
    except Exception as e:
        print(f"✗ Training step test failed: {e}")
        traceback.print_exc()
        return False


def test_device_memory_management():
    """Test device memory management."""
    print("\nTesting device memory management...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        
        # Create large tensors to test memory management
        large_tensors = []
        for i in range(5):
            tensor = torch.randn(1000, 1000).to(device)
            large_tensors.append(tensor)
        
        print("✓ Large tensors created")
        
        # Clear tensors
        del large_tensors
        
        # Trigger garbage collection based on device type
        if device_type == "cuda":
            torch.cuda.empty_cache()
            print("✓ CUDA cache cleared")
        elif device_type == "directml":
            import gc
            gc.collect()
            print("✓ DirectML garbage collection triggered")
        else:
            print("✓ CPU memory management (automatic)")
        
        return True
        
    except Exception as e:
        print(f"✗ Memory management test failed: {e}")
        traceback.print_exc()
        return False


def run_integration_tests():
    """Run all integration tests."""
    print("=" * 60)
    print("RLGymPPO DirectML Integration Tests")
    print("=" * 60)
    
    tests = [
        ("PPO Components", test_ppo_components),
        ("Experience Buffer Operations", test_experience_buffer_operations),
        ("Training Step", test_training_step),
        ("Device Memory Management", test_device_memory_management),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    # Print summary
    print("\n" + "=" * 60)
    print("Integration Test Summary")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✓ PASS" if passed_test else "✗ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed! RLGymPPO DirectML integration is working correctly.")
    else:
        print("⚠️  Some integration tests failed. Check the output above for details.")
    
    return passed == total


if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
