# DirectML Setup Guide for RLGym<PERSON>O

This guide provides detailed instructions for setting up DirectML with R<PERSON><PERSON><PERSON><PERSON><PERSON> for GPU acceleration on Windows systems.

## What is DirectML?

DirectML is Microsoft's hardware-accelerated machine learning library that provides GPU acceleration through DirectX 12. It offers several advantages:

- **Cross-vendor compatibility**: Works with AMD, Intel, and NVIDIA GPUs
- **Windows optimization**: Designed specifically for Windows systems
- **Broad hardware support**: Compatible with most DirectX 12-capable GPUs
- **Easy integration**: Simple PyTorch backend replacement

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10 version 1709 (Build 16299) or higher
- **GPU**: DirectX 12-compatible graphics card
- **Drivers**: Latest GPU drivers installed
- **Python**: Python 3.7 or higher

### Supported Hardware
- **AMD**: GCN 1st Generation (Radeon HD 7000 series) and newer
- **Intel**: Haswell (4th generation Core) integrated graphics and newer
- **NVIDIA**: Kepler architecture (GTX 600 series) and newer
- **Qualcomm**: Adreno GPUs (on supported devices)

## Installation Steps

### 1. Check System Compatibility

First, verify your Windows version:
```cmd
winver
```
Ensure you have Build 16299 or higher.

Check DirectX 12 support:
```cmd
dxdiag
```
Look for "DirectX Version: DirectX 12" in the System tab.

### 2. Update GPU Drivers

Update your GPU drivers through Windows Update:
1. Open Settings → Update & Security → Windows Update
2. Click "Check for updates"
3. Install any available driver updates
4. Restart your computer

### 3. Install Python Dependencies

Install the base RLGymPPO requirements:
```bash
pip install -r requirements.txt
```

### 4. Install DirectML

Install the torch-directml package:
```bash
pip install torch-directml
```

**Note**: torch-directml supports PyTorch up to version 2.3.1. The package will automatically install a compatible PyTorch version.

### 5. Verify Installation

Run the verification script:
```python
python -c "
import torch
import torch_directml
print('DirectML device:', torch_directml.device())
print('Test tensor:', torch.tensor([1, 2, 3]).to(torch_directml.device()))
"
```

If successful, you should see output similar to:
```
DirectML device: privateuseone:0
Test tensor: tensor([1, 2, 3], device='privateuseone:0')
```

## Usage Examples

### Basic Usage
```python
from rlgym_ppo import Learner

learner = Learner(
    env_create_function,
    device="directml",  # Use DirectML
    # ... other parameters
)
learner.learn()
```

### Automatic Device Selection
```python
from rlgym_ppo import Learner

learner = Learner(
    env_create_function,
    device="auto",  # Auto-select best device
    # ... other parameters
)
learner.learn()
```

### Device Information
```python
from rlgym_ppo.util import get_device_manager

dm = get_device_manager()
dm.log_device_info()
```

## Performance Optimization

### Memory Management
DirectML manages GPU memory automatically, but you can help by:
- Using appropriate batch sizes
- Clearing Python variables when done: `del large_tensor`
- Using garbage collection: `import gc; gc.collect()`

### Batch Size Tuning
Start with smaller batch sizes and increase gradually:
```python
learner = Learner(
    env_create_function,
    ppo_batch_size=25_000,  # Start smaller
    ppo_minibatch_size=12_500,
    device="directml"
)
```

### Model Size Considerations
For DirectML, consider slightly smaller models initially:
```python
learner = Learner(
    env_create_function,
    policy_layer_sizes=(256, 256, 256),  # vs (512, 512, 512)
    critic_layer_sizes=(256, 256, 256),
    device="directml"
)
```

## Troubleshooting

### Common Issues

#### 1. "torch-directml not found"
**Solution**: Install the package:
```bash
pip install torch-directml
```

#### 2. "DirectML device creation failed"
**Possible causes**:
- Outdated GPU drivers
- Incompatible GPU hardware
- Windows version too old

**Solutions**:
- Update GPU drivers through Windows Update
- Check hardware compatibility list
- Verify Windows version (winver)

#### 3. "Out of memory" errors
**Solutions**:
- Reduce batch sizes
- Reduce model size
- Close other GPU-intensive applications
- Use `device="auto"` to fall back to CPU if needed

#### 4. Poor performance compared to CUDA
**Notes**:
- DirectML performance varies by GPU generation
- Some NVIDIA GPUs may perform better with CUDA
- AMD and Intel GPUs typically benefit most from DirectML

### Debugging Steps

1. **Check device availability**:
   ```python
   from rlgym_ppo.util import get_device_manager
   dm = get_device_manager()
   print(dm.get_device_info())
   ```

2. **Test basic operations**:
   ```python
   import torch
   import torch_directml
   device = torch_directml.device()
   x = torch.randn(100, 100).to(device)
   y = torch.mm(x, x.t())
   print("DirectML test successful")
   ```

3. **Check for errors**:
   ```python
   from rlgym_ppo.util import get_device_manager
   dm = get_device_manager()
   errors = dm.get_initialization_errors()
   for error in errors:
       print(f"Error: {error}")
   ```

## Performance Comparison

### Expected Performance Gains
- **AMD GPUs**: 3-10x speedup vs CPU (depending on model)
- **Intel GPUs**: 2-5x speedup vs CPU (integrated graphics)
- **NVIDIA GPUs**: Comparable to CUDA, sometimes slower

### Benchmarking
To benchmark your setup:
```python
python example_directml.py
```

Monitor training speed (timesteps/second) and compare with CPU-only training.

## Advanced Configuration

### Environment Variables
You can set environment variables for debugging:
```cmd
set TORCH_DIRECTML_DEBUG=1
python your_script.py
```

### Mixed Precision (Experimental)
DirectML supports automatic mixed precision:
```python
# This is handled automatically by DirectML
# No additional configuration needed
```

## Getting Help

If you encounter issues:

1. Check the [DirectML GitHub Issues](https://github.com/microsoft/DirectML/issues)
2. Verify your setup with the verification script
3. Try the automatic device selection (`device="auto"`)
4. Check the troubleshooting section above

## Additional Resources

- [Microsoft DirectML Documentation](https://docs.microsoft.com/en-us/windows/ai/directml/)
- [PyTorch DirectML GitHub](https://github.com/microsoft/DirectML)
- [DirectML Hardware Compatibility](https://docs.microsoft.com/en-us/windows/ai/directml/gpu-compatibility)
