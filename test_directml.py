"""
DirectML Integration Test Suite

This script tests the DirectML integration with RLGymPPO to ensure
proper functionality and compatibility.
"""

import sys
import time
import traceback
from typing import Dict, Any

import torch
import numpy as np


def test_basic_imports():
    """Test that all required modules can be imported."""
    print("Testing basic imports...")
    
    try:
        from rlgym_ppo.util import get_device_manager, <PERSON><PERSON>Manager
        from rlgym_ppo.util.device_manager import ensure_tensor_device_compatibility
        print("✓ RLGymPPO device manager imports successful")
        return True
    except Exception as e:
        print(f"✗ Import failed: {e}")
        return False


def test_directml_availability():
    """Test DirectML package availability and basic functionality."""
    print("\nTesting DirectML availability...")
    
    try:
        import torch_directml
        print("✓ torch-directml package imported successfully")
        
        # Test device creation
        device = torch_directml.device()
        print(f"✓ DirectML device created: {device}")
        
        # Test basic tensor operations
        x = torch.tensor([1.0, 2.0, 3.0]).to(device)
        y = x * 2
        result = y.cpu().numpy()
        expected = np.array([2.0, 4.0, 6.0])
        
        if np.allclose(result, expected):
            print("✓ Basic DirectML tensor operations working")
            return True
        else:
            print(f"✗ Tensor operation mismatch: {result} != {expected}")
            return False
            
    except ImportError:
        print("✗ torch-directml package not available")
        return False
    except Exception as e:
        print(f"✗ DirectML test failed: {e}")
        traceback.print_exc()
        return False


def test_device_manager():
    """Test the device manager functionality."""
    print("\nTesting device manager...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        
        dm = get_device_manager()
        print("✓ Device manager created")
        
        # Test device info
        info = dm.get_device_info()
        print(f"✓ Device info retrieved: {info}")
        
        # Test device selection
        device, device_type = dm.get_device("auto")
        print(f"✓ Auto device selection: {device} (type: {device_type})")
        
        # Test DirectML specifically if available
        if info.get("directml_available", False):
            dml_device, dml_type = dm.get_device("directml")
            print(f"✓ DirectML device selection: {dml_device} (type: {dml_type})")
        else:
            print("ℹ DirectML not available, skipping DirectML-specific tests")
        
        return True
        
    except Exception as e:
        print(f"✗ Device manager test failed: {e}")
        traceback.print_exc()
        return False


def test_neural_network_compatibility():
    """Test neural network compatibility with DirectML."""
    print("\nTesting neural network compatibility...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        from rlgym_ppo.ppo import DiscreteFF, ValueEstimator
        
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        
        print(f"Testing with device: {device} (type: {device_type})")
        
        # Test policy network
        input_size = 107  # Typical RLGym observation size
        action_size = 8   # Typical action size
        layer_sizes = [256, 256]
        
        policy = DiscreteFF(input_size, action_size, layer_sizes, device)
        print("✓ Policy network created")
        
        # Test value network
        value_net = ValueEstimator(input_size, layer_sizes, device)
        print("✓ Value network created")
        
        # Test forward pass
        batch_size = 32
        obs = torch.randn(batch_size, input_size).to(device)
        
        # Policy forward pass
        policy_output = policy.get_output(obs)
        print(f"✓ Policy forward pass: {policy_output.shape}")
        
        # Value forward pass
        value_output = value_net(obs)
        print(f"✓ Value forward pass: {value_output.shape}")
        
        # Test action sampling
        action, log_prob = policy.get_action(obs[0])
        print(f"✓ Action sampling: action={action}, log_prob={log_prob}")
        
        return True
        
    except Exception as e:
        print(f"✗ Neural network test failed: {e}")
        traceback.print_exc()
        return False


def test_tensor_compatibility():
    """Test tensor device compatibility utilities."""
    print("\nTesting tensor compatibility utilities...")
    
    try:
        from rlgym_ppo.util import get_device_manager, ensure_tensor_device_compatibility
        
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        
        # Test tensor conversion
        cpu_tensor = torch.tensor([1, 2, 3], dtype=torch.int32)
        compatible_tensor = ensure_tensor_device_compatibility(cpu_tensor, device)
        
        print(f"✓ Tensor compatibility: {cpu_tensor.dtype} -> {compatible_tensor.dtype}")
        print(f"✓ Device transfer: {cpu_tensor.device} -> {compatible_tensor.device}")
        
        # Test that tensor is on correct device
        assert compatible_tensor.device == device
        assert compatible_tensor.dtype == torch.float32
        
        print("✓ Tensor compatibility utilities working")
        return True
        
    except Exception as e:
        print(f"✗ Tensor compatibility test failed: {e}")
        traceback.print_exc()
        return False


def test_performance_benchmark():
    """Run a simple performance benchmark."""
    print("\nRunning performance benchmark...")
    
    try:
        from rlgym_ppo.util import get_device_manager
        
        dm = get_device_manager()
        device, device_type = dm.get_device("auto")
        
        # Create test data
        batch_size = 1000
        input_size = 107
        hidden_size = 256
        
        x = torch.randn(batch_size, input_size).to(device)
        linear = torch.nn.Linear(input_size, hidden_size).to(device)
        
        # Warmup
        for _ in range(10):
            _ = linear(x)
        
        # Benchmark
        start_time = time.time()
        iterations = 100
        
        for _ in range(iterations):
            output = linear(x)
            loss = output.sum()
            loss.backward()
        
        end_time = time.time()
        
        total_time = end_time - start_time
        ops_per_second = iterations / total_time
        
        print(f"✓ Performance benchmark completed")
        print(f"  Device: {device} (type: {device_type})")
        print(f"  Operations per second: {ops_per_second:.2f}")
        print(f"  Total time: {total_time:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"✗ Performance benchmark failed: {e}")
        traceback.print_exc()
        return False


def run_all_tests() -> Dict[str, bool]:
    """Run all tests and return results."""
    print("=" * 60)
    print("DirectML Integration Test Suite")
    print("=" * 60)
    
    tests = [
        ("Basic Imports", test_basic_imports),
        ("DirectML Availability", test_directml_availability),
        ("Device Manager", test_device_manager),
        ("Neural Network Compatibility", test_neural_network_compatibility),
        ("Tensor Compatibility", test_tensor_compatibility),
        ("Performance Benchmark", test_performance_benchmark),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"✗ {test_name} failed with exception: {e}")
            results[test_name] = False
    
    return results


def print_summary(results: Dict[str, bool]):
    """Print test summary."""
    print("\n" + "=" * 60)
    print("Test Summary")
    print("=" * 60)
    
    passed = sum(results.values())
    total = len(results)
    
    for test_name, passed_test in results.items():
        status = "✓ PASS" if passed_test else "✗ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("-" * 60)
    print(f"Total: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DirectML integration is working correctly.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        
    return passed == total


if __name__ == "__main__":
    results = run_all_tests()
    success = print_summary(results)
    
    sys.exit(0 if success else 1)
